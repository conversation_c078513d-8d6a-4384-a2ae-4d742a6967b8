<template>
    <tui-bottom-popup :show="show" @close="handleClose" :z-index="1000" :height="557 * 2">
        <view class="shopInfo-popup">
            <!-- 头部显示商品信息 -->
            <view class="shopInfo-popup-header">
                <view class="picture-wrapper">
                    <image src="https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg"
                        mode="widthFix"></image>
                </view>
                <view class="good-info-wrapper">
                    <view>
                        <text class="price-symbol">￥</text>
                        <text class="price-value">164.00</text>
                    </view>
                    <view class="good-info-wrapper-bottom">
                        <text class="price-confirm">已选</text>
                        <text class="price-name">原味燕窝胶原饮90g</text>
                    </view>
                </view>
            </view>
            <!-- 商品列表展示部分 -->
            <view class="shopInfo-popup-list">
                <view class="showInfo-popup-list-header">
                    <text>规格</text>
                    <text class="iconfont ">列表</text>
                </view>
            </view>
        </view>
    </tui-bottom-popup>
</template>

<script>
import tuiBottomPopup from '@/components/base/tui-bottom-popup.vue';
export default {
    components: {
        tuiBottomPopup
    },
    emits: ['close'],
    props: {
        shopInfo: {
            type: Object,
            default: () => { }
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleClose() {
            this.$emit('close');
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../index.scss";

.shopInfo-popup {
    z-index: 1000;
    height: 100%;
    padding-top: 60rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
}

// header
.shopInfo-popup-header {
    display: flex;
    align-content: center;
    gap: 14rpx;
    margin-bottom: 46rpx;
}

.good-info-wrapper {
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .price-confirm {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
    }

    .price-symbol {
        color: #ff0000;
        font-size: 28rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .price-value {
        color: #ff0000;
        font-size: 40rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .good-info-wrapper-bottom {
        display: flex;
        gap: 12rpx;
        align-items: center;
    }
}

// 商品列表展示部分
.shopInfo-popup-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
}

.showInfo-popup-list-header {
    display: flex;
    justify-content: space-between;

    text:nth-child(1) {
        color: #131313;
        font-size: 28rpx;
        font-weight: 500;
    }

    text:nth-child(2) {
        color: #131313;
        font-size: 24rpx;
        font-weight: 400;
    }

}
</style>